# Microsoft Presidio-Enhanced LLM Gateway Requirements
# Enterprise-grade PII detection with ML capabilities

# Core Microsoft Presidio libraries
presidio-analyzer==2.2.354
presidio-anonymizer==2.2.354

# NLP and ML dependencies for Presidio
spacy==3.7.2
https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl

# Additional ML libraries
transformers==4.36.0
torch==2.1.2
numpy==1.24.3

# Web framework (lightweight)
# Note: Using built-in http.server for Railway compatibility

# Validation and utility libraries
validators==0.22.0
phonenumbers==8.13.26

# Performance and caching (optional)
# redis==5.0.1  # Commented out for Railway simplicity

# Security and cryptography
cryptography==41.0.8

# Data processing
python-dateutil==2.8.2

# Logging and monitoring
structlog==23.2.0

# Environment management
python-dotenv==1.0.0

# Note: This requirements file is for full Presidio functionality
# For Railway deployment, some dependencies might need adjustment
# The gateway includes graceful fallback to regex patterns if Presidio fails to load
