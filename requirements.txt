# Microsoft Presidio-Enhanced LLM Gateway Requirements
# Minimal Railway-compatible setup

# Core Presidio (try minimal first)
presidio-analyzer>=2.2.0
presidio-anonymizer>=2.2.0

# Essential NLP (lighter approach)
spacy>=3.4.0,<3.8.0

# Validation utilities (lightweight)
validators>=0.20.0
phonenumbers>=8.12.0

# Basic security
cryptography>=3.4.0

# Utilities
python-dateutil>=2.8.0
python-dotenv>=0.19.0

# Note: spaCy model will be downloaded at runtime if needed
# This reduces build time and size
